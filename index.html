<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title><PERSON><PERSON><PERSON></title>

    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/unicons.css">
    <link rel="stylesheet" href="css/owl.carousel.min.css">
    <link rel="stylesheet" href="css/owl.theme.default.min.css">
    <link rel="icon" href="images/icon/seo.png">
    <!-- MAIN STYLE -->
    <link rel="stylesheet" href="css/style.css">

</head>

<body>

    <!-- MENU -->
    <nav class="navbar navbar-expand-sm navbar-light">
        <div class="container">
            <a class="navbar-brand" href="index.html"><i class='uil uil-user'></i> Moetez</a>

            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
                <span class="navbar-toggler-icon"></span>
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a href="#about" class="nav-link"><span data-hover="About">About</span></a>
                    </li>
                    <li class="nav-item">
                        <a href="#project" class="nav-link"><span data-hover="Projects">Projects</span></a>
                    </li>
                    <li class="nav-item">
                        <a href="#resume" class="nav-link"><span data-hover="Resume">Resume</span></a>
                    </li>
                    <li class="nav-item">
                        <a href="#skills" class="nav-link"><span data-hover="Skills">Skills</span></a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link"><span data-hover="Contact">Contact</span></a>
                    </li>
                </ul>

                <ul class="navbar-nav ml-lg-auto">
                    <div class="ml-lg-4">
                        <div class="color-mode d-lg-flex justify-content-center align-items-center">
                            <i class="color-mode-icon"></i> Color mode
                        </div>
                    </div>
                </ul>
                <div class="language-menu ml-lg-4">
                    <button class="language-button">
                      <img id="language_img" src="images/icon/en.png" alt="British flag" class="language-flag">
                      <span id="language_text" class="language-label">English</span>
                    </button>
                    <ul class="language-dropdown">
                        <li>
                            <a id="en_button">
                                <img src="images/icon/en.png" alt="British flag">
                                <span>English</span>
                            </a>
                        </li>
                        <li>
                            <a id="fr_button">
                                <img src="images/icon/fr.png" alt="French flag">
                                <span>Français</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- ABOUT -->
    <section class="about full-screen d-lg-flex justify-content-center align-items-center" id="about">
        <div class="container">
            <div class="row">

                <div class="col-lg-7 col-md-12 col-12 d-flex align-items-center">
                    <div class="about-text">
                        <small id="port" class="small-text">Welcome to <span class="mobile-block">my portfolio website!</span></small>
                        <h1 class="animated animated-text">
                            <span id="heyfolks" class="mr-2">Hey folks, I'm</span>
                            <div class="animated-info">
                                <span class="animated-item">Moetez Baklouti</span>
                                <span id="im1" class="animated-item">Web Developer</span>
                                <span id="im2" class="animated-item">Ethical Hacker</span>
                                <span id="im3" class="animated-item">Security Specialist</span>
                            </div>
                        </h1>

                        <p id="sum">Developing a successful product is a challenge. As a backend developer, I am highly skilled in building robust and scalable systems to support efficient data exchange and processing.</p>

                        <div class="custom-btn-group mt-4">
                            <a id="download_resume" download target="_blank" class="btn mr-lg-2 custom-btn"><i class='uil uil-file-alt'></i> Download Resume</a>
                            <a id="getquote" href="#contact" class="btn custom-btn custom-btn-bg custom-btn-link">Get a free quote</a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-5 col-md-12 col-12">
                    <div class="about-image svg">
                        <img src="images/undraw/undraw_developer_activity_re_39tg.svg" class="img-fluid" alt="svg image">
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- PROJECTS -->
    <section class="project py-5" id="project">
        <div class="container">

            <div class="row">
                <div class="col-lg-11 text-center mx-auto col-12">

                    <div class="col-lg-8 mx-auto">
                        <h2 id="things_developed">Things I have developed</h2>
                    </div>

                    <div class="projects-grid">
                        <!-- NOTE: Replace placeholder images (graph.png) with actual project images -->
                        <div class="project-card" data-project="paradol">
                            <div class="project-info">
                                <img src="images/project/paradol.png" class="img-fluid" alt="Paradol project">
                                <div class="project-overlay">
                                    <div class="project-content">
                                        <h3 class="project-title">Paradol Parapharmacy</h3>
                                        <p class="project-description">E-commerce parapharmacy store built with WordPress for a client, featuring product catalog and online ordering.</p>
                                        <div class="project-tech">
                                            <span class="tech-tag">WordPress</span>
                                            <span class="tech-tag">E-commerce</span>
                                            <span class="tech-tag">PHP</span>
                                        </div>
                                        <a href="#" class="project-link" data-project="paradol" target="_blank">
                                            <i class="uil uil-external-link-alt"></i> View Project
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="project-card" data-project="juice-up">
                            <div class="project-info">
                                <img src="images/project/juiceup.png" class="img-fluid" alt="Juice Up project">
                                <div class="project-overlay">
                                    <div class="project-content">
                                        <h3 class="project-title">Juice Up Store</h3>
                                        <p class="project-description">Modern juice e-commerce website with responsive design and smooth user experience for a client.</p>
                                        <div class="project-tech">
                                            <span class="tech-tag">React</span>
                                            <span class="tech-tag">E-commerce</span>
                                        </div>
                                        <a href="#" class="project-link" data-project="juice-up" target="_blank">
                                            <i class="uil uil-external-link-alt"></i> View Project
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="project-card" data-project="maison-merveille">
                            <div class="project-info">
                                <img src="images/project/maisonmerveille.png" class="img-fluid" alt="Maison Merveille project">
                                <div class="project-overlay">
                                    <div class="project-content">
                                        <h3 class="project-title">Maison Merveille</h3>
                                        <p class="project-description">Elegant clothing e-commerce platform with modern design and seamless shopping experience.</p>
                                        <div class="project-tech">
                                            <span class="tech-tag">React</span>
                                            <span class="tech-tag">Fashion</span>
                                        </div>
                                        <a href="#" class="project-link" data-project="maison-merveille" target="_blank">
                                            <i class="uil uil-external-link-alt"></i> View Project
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="project-card" data-project="ai-tools">
                            <div class="project-info">
                                <img src="images/project/ai-tools.png" class="img-fluid" alt="AI Tools project">
                                <div class="project-overlay">
                                    <div class="project-content">
                                        <h3 class="project-title">AI Tools Generator</h3>
                                        <p class="project-description">GitHub workflow generator powered by AI to automate development processes and CI/CD pipelines.</p>
                                        <div class="project-tech">
                                            <span class="tech-tag">AI</span>
                                            <span class="tech-tag">GitHub Actions</span>
                                            <span class="tech-tag">Automation</span>
                                        </div>
                                        <a href="#" class="project-link" data-project="ai-tools" target="_blank">
                                            <i class="uil uil-external-link-alt"></i> View Project
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="project-card" data-project="rag-chat">
                            <div class="project-info">
                                <img src="images/project/rag-chat.png" class="img-fluid" alt="RAG Chat project">
                                <div class="project-overlay">
                                    <div class="project-content">
                                        <h3 class="project-title">RAG AI Chat System</h3>
                                        <p class="project-description">Advanced RAG AI chat system for intelligent document-based conversations.</p>
                                        <div class="project-tech">
                                            <span class="tech-tag">AI</span>
                                            <span class="tech-tag">RAG</span>
                                            <span class="tech-tag">AI Agents</span>
                                        </div>
                                        <a href="#" class="project-link" data-project="rag-chat" target="_blank">
                                            <i class="uil uil-external-link-alt"></i> View Project
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="project-card" data-project="vps-admin">
                            <div class="project-info">
                                <img src="images/project/vps-admin-chat.png" class="img-fluid" alt="VPS Admin project">
                                <div class="project-overlay">
                                    <div class="project-content">
                                        <h3 class="project-title">VPS AI Admin</h3>
                                        <p class="project-description">AI-powered VPS administration tool with SSH integration for automated server management and monitoring.</p>
                                        <div class="project-tech">
                                            <span class="tech-tag">AI</span>
                                            <span class="tech-tag">SSH</span>
                                            <span class="tech-tag">Devops</span>
                                        </div>
                                        <a href="#" class="project-link" data-project="vps-admin" target="_blank">
                                            <i class="uil uil-external-link-alt"></i> View Project
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </section>

    <!-- FEATURES -->
    <section class="resume py-5 d-lg-flex justify-content-center align-items-center" id="resume">
        <div class="container">
            <div class="row">

                <div class="col-lg-6 col-12">
                    <h2 id="experiences" class="mb-4">Experiences</h2>

                    <div class="timeline">
                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2024</span>
                            </div>
                            <div class="timeline-info">
                                <h3><span id="job_title_1">Security Operations Intern</span><small>National Center of Information Technology (CNI)</small></h3>
                                <p id="job_description_1">Conducted study on Security Operations Center (SOC) principles and architecture. Implemented a proof-of-concept SOC using open-source tools: Wazuh (SIEM/XDR), TheHive (SIRP), Cortex (Analysis), and Shuffle (SOAR). Gained experience in security event monitoring, alert triage, and incident response workflow automation.</p>
                            </div>
                        </div>

                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2023</span>
                            </div>
                            <div class="timeline-info">
                                <h3><span id="job_title_2">Development Intern</span><small>IVA</small></h3>
                                <p id="job_description_2">Contributed to the development of an AI-powered virtual agent application. Integrated third-party services via APIs (Shopify, Gorgias, Zoho SalesIQ) to extend platform capabilities. Developed web components and features using Next.js framework.</p>
                            </div>
                        </div>

                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2022</span>
                            </div>
                            <div class="timeline-info">
                                <h3><span id="job_title_3">Java Backend Developer Internship</span><small>Xtensus</small></h3>
                                <p id="job_description_3">As a Java backend developer, I bring a wealth of skills and experience to any team. During my internship, I successfully completed several important tasks, including conducting research on a project topic and developing
                                    a solution for exchanging data via generic REST webservices.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-12">
                    <h2 id="educations" class="mb-4 mobile-mt-2">Educations</h2>

                    <div class="timeline">
                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2024</span>
                            </div>
                            <div class="timeline-info">
                                <h3><span id="degree3">Cyber Security</span><small id="location3">Higher Institute of Management of Tunis</small></h3>
                                <p id="description3">Covering topics such as computer networks, cryptography, data protection, and cybersecurity governance. as well as practical experience in implementing cybersecurity solutions.</p>
                            </div>
                        </div>

                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2022</span>
                            </div>
                            <div class="timeline-info">
                                <h3><span id="degree2">Business Computing: Business Information System</span><small id="location2">Higher Institute of Management of Tunis</small></h3>
                                <p id="description2">I learnt how to use technology for business process management, decision-making, and performance improvement with practical skills in software development, data management, system analysis, and an understanding of the business
                                    context in which these systems operate.</p>
                            </div>
                        </div>

                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2019</span>
                            </div>
                            <div class="timeline-info">
                                <h3><span id="degree1">Bachelor's degree in computer science</span><small>Ibn Abi Dhiaf</small></h3>
                                <p id="description1">It Covers a wide range of topics, including algorithms, data structures, computer architecture, software engineering, and database systems. I learnt programming languages such as C, Java, and Python, as well as web development
                                    technologies such as HTML, CSS, and JavaScript.</p>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- SKILLS -->
    <section class="py-5 d-lg-flex justify-content-center align-items-center" id="skills">
        <div class="skills-section">
            <div class="skills-header">
                <h1 id="skills_title">Skills</h1>
            </div>
            <div class="skills-container">

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/html.png" alt="" class="skills-icons">
                        </div>
                        <h3>HTML 5</h3>
                    </div>

                </div>


                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/css.png" alt="" class="skills-icons">
                        </div>
                        <h3>CSS 3</h3>
                    </div>

                </div>


                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/javascript.png" alt="" class="skills-icons">
                        </div>
                        <h3>JAVASCRIPT</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/reactjs.png" alt="" class="skills-icons">
                        </div>
                        <h3>REACT JS</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img style="width: 120px;" src="images/skills/java.png" alt="" class="skills-icons">
                        </div>
                        <h3>JAVA</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/spring.svg" alt="" class="skills-icons">
                        </div>
                        <h3>SPRING</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/spring_boot.png" alt="" class="skills-icons">
                        </div>
                        <h3>SPRING BOOT</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/alfresco.png" alt="" class="skills-icons">
                        </div>
                        <h3>ALFRESCO</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/github.png" alt="" class="skills-icons">
                        </div>
                        <h3>GIT & GITHUB</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/unity.png" alt="" class="skills-icons">
                        </div>
                        <h3>UNITY & C#</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/windows.png" alt="" class="skills-icons">
                        </div>
                        <h3>WINDOWS</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/linux.png" alt="" class="skills-icons">
                        </div>
                        <h3>LINUX</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/mysql.png" alt="" class="skills-icons">
                        </div>
                        <h3>MYSQL</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/postgresql.svg" alt="" class="skills-icons">
                        </div>
                        <h3>POSTRESQL</h3>
                    </div>

                </div>

                <div class="skills-box">
                    <div class="skills-title">
                        <div class="skills-img">
                            <img src="images/skills/mongodb.svg" alt="" class="skills-icons">
                        </div>
                        <h3>MONGODB</h3>
                    </div>
                </div>

            </div>

        </div>
    </section>
    <!-- CONTACT -->
    <section class="contact py-5" id="contact">
        <div class="container">
            <div class="row">

                <div class="col-lg-5 mr-lg-5 col-12">
                    <div class="google-map w-100">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3194.8517769788505!2d10.085277515572885!3d36.79810407551676!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12fd320ea1eae67f%3A0xa9116ff514f63de3!2sCit%C3%A9%20Ben%20Younes%2C%20Manouba!5e0!3m2!1sen!2stn!4v1679434852122!5m2!1sen!2stn"
                            width="400" height="300" frameborder="0" style="border:0" allowfullscreen></iframe>
                    </div>

                    <div class="contact-info d-flex justify-content-between align-items-center py-4 px-lg-5">
                        <div class="contact-info-item">
                            <h3 id="map_text" class="mb-3 text-white">Say hello</h3>
                            <p class="footer-text mb-0">+216 58 922 975</p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>

                        <ul class="social-links">
                            <li>
                                <a href="https://www.facebook.com/Baklouti.Moetez/" target="_blank" class="uil" data-toggle="tooltip" data-placement="left" title="Facebook"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                                    <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
                                  </svg></a>
                            </li>
                            <li>
                                <a href="https://github.com/Moetez-Baklouti" target="_blank" class="uil" data-toggle="tooltip" data-placement="left" title="Github"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-github" viewBox="0 0 16 16">
                                    <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"/>
                                  </svg></a>
                            </li>
                            <li>
                                <a href="https://baklouti.itch.io/" target="_blank" class="uil" data-toggle="tooltip" data-placement="left" title="Itch io">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-shop" viewBox="0 0 16 16">
                                        <path d="M2.97 1.35A1 1 0 0 1 3.73 1h8.54a1 1 0 0 1 .76.35l2.609 3.044A1.5 1.5 0 0 1 16 5.37v.255a2.375 2.375 0 0 1-4.25 1.458A2.371 2.371 0 0 1 9.875 8 2.37 2.37 0 0 1 8 7.083 2.37 2.37 0 0 1 6.125 8a2.37 2.37 0 0 1-1.875-.917A2.375 2.375 0 0 1 0 5.625V5.37a1.5 1.5 0 0 1 .361-.976l2.61-3.045zm1.78 4.275a1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0 1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0 1.375 1.375 0 1 0 2.75 0V5.37a.5.5 0 0 0-.12-.325L12.27 2H3.73L1.12 5.045A.5.5 0 0 0 1 5.37v.255a1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0zM1.5 8.5A.5.5 0 0 1 2 9v6h1v-5a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v5h6V9a.5.5 0 0 1 1 0v6h.5a.5.5 0 0 1 0 1H.5a.5.5 0 0 1 0-1H1V9a.5.5 0 0 1 .5-.5zM4 15h3v-5H4v5zm5-5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-3zm3 0h-2v3h2v-3z"/>
                                    </svg>
                                </a>

                            </li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-6 col-12">
                    <div class="contact-form">
                        <h2 id="contact_header" class="mb-4">Interested to work together? Let's talk</h2>

                        <form action="https://formsubmit.co/a340bfb4003d5703588ae7e46b211c77" method="POST">
                            <div class="row">
                                <div class="col-lg-6 col-12">
                                    <input type="text" class="form-control" name="name" placeholder="Your Name" id="name">
                                </div>

                                <div class="col-lg-6 col-12">
                                    <input type="email" class="form-control" name="email" placeholder="Email" id="email">
                                </div>

                                <div class="col-12">
                                    <textarea name="message" rows="6" class="form-control" id="message" placeholder="Message"></textarea>
                                </div>

                                <div class="ml-lg-auto col-lg-5 col-12">
                                    <input id="send" type="submit" class="form-control submit-btn" value="Send">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- FOOTER -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">

                <div class="col-lg-12 col-12">
                    <p class="copyright-text text-center">Copyright &copy;
                        <Y id="year"></Y> Moetez Baklouti.
                        <Y id="copyright">All rights reserved</Y>
                    </p>
                </div>

            </div>
        </div>
    </footer>

    <script src="js/jquery-3.3.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/Headroom.js"></script>
    <script src="js/jQuery.headroom.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/smoothscroll.js"></script>
    <script src="js/custom.js"></script>

</body>

</html>