(function($) {

    "use strict";

    // COLOR MODE
    $('.color-mode').click(function() {
        $('.color-mode-icon').toggleClass('active')
        $('body').toggleClass('dark-mode')
    })

    // HEADER
    $(".navbar").headroom();

    // PROJECT GRID - No carousel needed anymore

    // SMOOTHSCROLL
    $(function() {
        $('.nav-link, .custom-btn-link').on('click', function(event) {
            var $anchor = $(this);
            $('html, body').stop().animate({
                scrollTop: $($anchor.attr('href')).offset().top - 49
            }, 1000);
            event.preventDefault();
        });
    });

    // TOOLTIP
    $('.social-links a').tooltip();

    // PROJECT LINKS
    $('.project-link').on('click', function(e) {
        e.preventDefault();
        const projectName = $(this).data('project');

        // Define project URLs
        const projectUrls = {
            'paradol': 'https://paradol.tn/',
            'juice-up': 'https://juice-up-sable.vercel.app/',
            'maison-merveille': 'https://maison-merveille.vercel.app/',
            'ai-tools': 'https://ai-tools-red.vercel.app/',
            'rag-chat': 'https://rag-chatapp.baklouti.giize.com/',
            'vps-admin': 'https://vps-admin-app.baklouti.giize.com/'
        };

        // Open project URL in new tab
        if (projectUrls[projectName]) {
            window.open(projectUrls[projectName], '_blank');
        } else {
            // Fallback - you can customize this message
            const message = language === 'fr' ? 'Lien du projet bientôt disponible!' : 'Project link will be available soon!';
            alert(message);
        }
    });

})(jQuery);

// Function to update project content based on language - using vanilla JavaScript
function updateProjectContent() {
    const projects = {
        'paradol': {
            en: {
                title: 'Paradol Parapharmacy',
                description: 'E-commerce parapharmacy store built with WordPress for a client, featuring product catalog and online ordering.',
                tech: ['WordPress', 'E-commerce', 'PHP']
            },
            fr: {
                title: 'Parapharmacie Paradol',
                description: 'Boutique e-commerce de parapharmacie construite avec WordPress pour un client, avec catalogue de produits et commande en ligne.',
                tech: ['WordPress', 'E-commerce', 'PHP']
            }
        },
        'juice-up': {
            en: {
                title: 'Juice Up Store',
                description: 'Modern juice e-commerce website with responsive design and smooth user experience for a client.',
                tech: ['React', 'E-commerce']
            },
            fr: {
                title: 'Boutique Juice Up',
                description: 'Site e-commerce moderne de jus avec design responsive et expérience utilisateur fluide pour un client.',
                tech: ['React', 'E-commerce']
            }
        },
        'maison-merveille': {
            en: {
                title: 'Maison Merveille',
                description: 'Elegant clothing e-commerce platform with modern design and seamless shopping experience.',
                tech: ['React','E-commerce', 'Fashion']
            },
            fr: {
                title: 'Maison Merveille',
                description: 'Plateforme e-commerce de vêtements élégante avec design moderne et expérience d\'achat fluide.',
                tech: ['React','E-commerce', 'Mode']
            }
        },
        'ai-tools': {
            en: {
                title: 'AI Tools Generator',
                description: 'GitHub workflow generator powered by AI to automate development processes and CI/CD pipelines.',
                tech: ['AI', 'GitHub Actions', 'Automation']
            },
            fr: {
                title: 'Générateur d\'Outils IA',
                description: 'Générateur de workflows GitHub alimenté par l\'IA pour automatiser les processus de développement et les pipelines CI/CD.',
                tech: ['IA', 'GitHub Actions', 'Automatisation']
            }
        },
        'rag-chat': {
            en: {
                title: 'RAG AI Chat System',
                description: 'Advanced RAG AI chat system for intelligent document-based conversations.',
                tech: ['AI', 'RAG', 'Chat System']
            },
            fr: {
                title: 'Système de Chat IA RAG',
                description: 'Système de chat IA RAG avancé pour des conversations intelligentes basées sur des documents.',
                tech: ['IA', 'RAG', 'Système de Chat']
            }
        },
        'vps-admin': {
            en: {
                title: 'VPS AI Admin',
                description: 'AI-powered VPS administration tool with SSH integration for automated server management and monitoring.',
                tech: ['AI', 'SSH', 'Server Admin']
            },
            fr: {
                title: 'Admin IA VPS',
                description: 'Outil d\'administration VPS alimenté par l\'IA avec intégration SSH pour la gestion et surveillance automatisées des serveurs.',
                tech: ['IA', 'SSH', 'Admin Serveur']
            }
        }
    };

    // Define project URLs for checking availability
    const projectUrls = {
        'paradol': 'https://paradol.tn/',
        'juice-up': 'https://juice-up-sable.vercel.app/',
        'maison-merveille': 'https://maison-merveille.vercel.app/',
        'ai-tools': 'https://ai-tools-red.vercel.app/',
        'rag-chat': 'https://rag-chatapp.baklouti.giize.com/',
        'vps-admin': 'https://vps-admin-app.baklouti.giize.com/'
    };

    // Update each project card using vanilla JavaScript
    Object.keys(projects).forEach(projectKey => {
        const project = projects[projectKey];
        const currentLang = language || 'en';
        const content = project[currentLang];

        // Find project card using vanilla JavaScript
        const projectCard = document.querySelector(`.project-card[data-project="${projectKey}"]`);
        if (projectCard) {
            // Update title
            const titleElement = projectCard.querySelector('.project-title');
            if (titleElement) {
                titleElement.textContent = content.title;
            }

            // Update description
            const descriptionElement = projectCard.querySelector('.project-description');
            if (descriptionElement) {
                descriptionElement.textContent = content.description;
            }

            // Update tech tags
            const techContainer = projectCard.querySelector('.project-tech');
            if (techContainer) {
                techContainer.innerHTML = '';
                content.tech.forEach(tech => {
                    const techTag = document.createElement('span');
                    techTag.className = 'tech-tag';
                    techTag.textContent = tech;
                    techContainer.appendChild(techTag);
                });
            }

            // Show/hide project link based on URL availability
            const projectLink = projectCard.querySelector('.project-link');
            if (projectLink) {
                if (projectUrls[projectKey]) {
                    projectLink.style.display = '';
                    // Update button text based on language
                    const buttonText = currentLang === 'fr' ? 'Voir le Projet' : 'View Project';
                    projectLink.innerHTML = `<i class="uil uil-external-link-alt"></i> ${buttonText}`;
                } else {
                    projectLink.style.display = 'none';
                }
            }
        }
    });
}

document.getElementById("year").innerText = new Date().getFullYear();
let language = "en"
const language_img = document.getElementById("language_img");
const language_text = document.getElementById("language_text");
const en_button = document.getElementById("en_button");
const fr_button = document.getElementById("fr_button");
const download_resume = document.getElementById("download_resume");

if (language === "en") {
    download_resume.href = "downloads/resume.pdf";
} else {
    download_resume.href = "downloads/cv.pdf";
}

// Initialize project content on page load
document.addEventListener('DOMContentLoaded', function() {
    updateProjectContent();
});

en_button.addEventListener("click", () => {
    if (language !== "en") {
        language_img.src = "images/icon/en.png";
        language_text.innerText = "English";
        download_resume.href = "downloads/resume.pdf";
        document.getElementById("copyright").innerText = "All rights reserved";
        document.getElementById("contact_header").innerText = "Interested to work together? Let's talk";
        document.getElementById("name").placeholder = "Your Name";
        document.getElementById("send").value = "Send";
        document.getElementById("map_text").innerText = "Say hello";
        document.getElementById("skills_title").innerText = "Skills";
        document.getElementById("things_developed").innerText = "Things I have developed";
        document.getElementById("job_title_1").innerText = "Security Operations Intern";
        document.getElementById("job_description_1").innerText = "Conducted study on Security Operations Center (SOC) principles and architecture. Implemented a proof-of-concept SOC using open-source tools: Wazuh (SIEM/XDR), TheHive (SIRP), Cortex (Analysis), and Shuffle (SOAR). Gained experience in security event monitoring, alert triage, and incident response workflow automation.";
        document.getElementById("job_title_2").innerText = "Development Intern";
        document.getElementById("job_description_2").innerText = "Contributed to the development of an AI-powered virtual agent application. Integrated third-party services via APIs (Shopify, Gorgias, Zoho SalesIQ) to extend platform capabilities. Developed web components and features using Next.js framework.";
        document.getElementById("job_title_3").innerText = "Java Backend Developer Internship";
        document.getElementById("job_description_3").innerText = "As a Java backend developer, I bring a wealth of skills and experience to any team. During my internship, I successfully completed several important tasks, including conducting research on a project topic and developing a solution for exchanging data via generic REST webservices.";
        document.getElementById("degree1").innerText = "Bachelor's degree in computer science";
        document.getElementById("description1").innerText = "It Covers a wide range of topics, including algorithms, data structures, computer architecture, software engineering, and database systems. I learnt programming languages such as C, Java, and Python, as well as web development technologies such as HTML, CSS, and JavaScript.";
        document.getElementById("degree2").innerText = "Business Computing: Business Information System";
        document.getElementById("location2").innerText = "Higher Institute of Management of Tunis";
        document.getElementById("description2").innerText = "I learnt how to use technology for business process management, decision-making, and performance improvement with practical skills in software development, data management, system analysis, and an understanding of the business context in which these systems operate.";
        document.getElementById("degree3").innerText = "Cyber Security";
        document.getElementById("location3").innerText = "Higher Institute of Management of Tunis";
        document.getElementById("description3").innerText = "Covering topics such as computer networks, cryptography, data protection, and cybersecurity governance. as well as practical experience in implementing cybersecurity solutions.";
        document.getElementById("educations").innerText = "Educations";
        document.getElementById("experiences").innerText = "Experiences";
        document.getElementById("sum").innerText = "Developing a successful product is a challenge. As a backend developer, I am highly skilled in building robust and scalable systems to support efficient data exchange and processing.";
        download_resume.innerText = "Download Resume";
        document.getElementById("getquote").innerText = "Get a free quote";
        document.getElementById("port").innerHTML = 'Welcome to <span id="port2" class="mobile-block">my portfolio website!</span>';
        document.getElementById("heyfolks").innerText = "Hey folks, I'm";
        document.getElementById("im1").innerText = "Web Developer";
        document.getElementById("im2").innerText = "Ethical Hacker";
        document.getElementById("im3").innerText = "Security Specialist";
        // Update navigation menu
        const aboutLink = document.querySelector('a[href="#about"] span');
        const projectLink = document.querySelector('a[href="#project"] span');
        const resumeLink = document.querySelector('a[href="#resume"] span');
        const skillsLink = document.querySelector('a[href="#skills"] span');
        const contactLink = document.querySelector('a[href="#contact"] span');

        if (aboutLink) aboutLink.textContent = "About";
        if (projectLink) projectLink.textContent = "Projects";
        if (resumeLink) resumeLink.textContent = "Resume";
        if (skillsLink) skillsLink.textContent = "Skills";
        if (contactLink) contactLink.textContent = "Contact";
        language = "en";
        updateProjectContent();
    }
})

fr_button.addEventListener("click", () => {
    if (language !== "fr") {
        language_img.src = "images/icon/fr.png";
        language_text.innerText = "Français";
        download_resume.href = "downloads/cv.pdf";
        document.getElementById("copyright").innerText = "Tous les droits sont réservés";
        document.getElementById("contact_header").innerText = "Intéressé à travailler ensemble? écris moi";
        document.getElementById("name").placeholder = "Votre nom";
        document.getElementById("send").value = "Envoyer";
        document.getElementById("map_text").innerText = "Bonjour";
        document.getElementById("skills_title").innerText = "Compétences";
        document.getElementById("things_developed").innerText = "Choses que j'ai développées";
        document.getElementById("job_title_1").innerText = "Stagiaire Opérations de Sécurité";
        document.getElementById("job_description_1").innerText = "Réalisation d'une étude sur les principes et l'architecture des centres d'opérations de sécurité (SOC). Implémentation d'un SOC de démonstration utilisant des outils open-source : Wazuh (SIEM/XDR), TheHive (SIRP), Cortex (Analyse), et Shuffle (SOAR). Acquisition d'expérience en surveillance d'événements de sécurité, triage d'alertes, et automatisation des flux de réponse aux incidents.";
        document.getElementById("job_title_2").innerText = "Stagiaire Développement";
        document.getElementById("job_description_2").innerText = "Contribution au développement d'une application d'agent virtuel alimentée par l'IA. Intégration de services tiers via des API (Shopify, Gorgias, Zoho SalesIQ) pour étendre les capacités de la plateforme. Développement de composants web et de fonctionnalités utilisant le framework Next.js.";
        document.getElementById("job_title_3").innerText = "Stage Développeur Backend Java";
        document.getElementById("job_description_3").innerText = "En tant que développeur backend Java, j'apporte une grande richesse de compétences et d'expérience à n'importe quelle équipe. Au cours de mon stage, j'ai réussi à accomplir plusieurs tâches importantes, notamment la recherche sur un sujet de projet et le développement d'une solution pour l'échange de données via des webservices REST génériques.";
        document.getElementById("degree1").innerText = "Baccalauréat Informatique";
        document.getElementById("description1").innerText = "Cela couvre un large éventail de sujets, notamment les algorithmes, les structures de données, l'architecture informatique, le génie logiciel et les systèmes de base de données. J'ai appris des langages de programmation tels que C, Java et Python, ainsi que des technologies de développement web telles que HTML, CSS et JavaScript.";
        document.getElementById("degree2").innerText = "Informatique de gestion: Système d'Information d'Entreprise";
        document.getElementById("location2").innerText = "Institut Supérieur de Gestion de Tunis";
        document.getElementById("description2").innerText = "J'ai appris à utiliser la technologie pour la gestion des processus commerciaux, la prise de décisions et l'amélioration des performances, avec des compétences pratiques en développement de logiciels, gestion de données, analyse de systèmes et une compréhension du contexte commercial dans lequel ces systèmes opèrent.";
        document.getElementById("degree3").innerText = "Cyber-Sécurité";
        document.getElementById("location3").innerText = "Institut Supérieur de Gestion de Tunis";
        document.getElementById("description3").innerText = "Couvrant des sujets tels que les réseaux informatiques, la cryptographie, la protection des données et la gouvernance de la cybersécurité, ainsi qu'une expérience pratique dans la mise en œuvre de solutions de cybersécurité.";
        document.getElementById("educations").innerText = "Éducations";
        document.getElementById("experiences").innerText = "Expériences";
        document.getElementById("sum").innerText = "Développer un produit réussi est un défi. En tant que développeur backend, je suis hautement qualifié pour construire des systèmes robustes et évolutifs pour soutenir l'échange et le traitement efficaces de données.";
        download_resume.innerText = "Télécharger le CV";
        document.getElementById("getquote").innerText = "Contactez moi";
        document.getElementById("port").innerHTML = 'Bienvenue sur <span id="port2" class="mobile-block">mon site portfolio !</span>';
        document.getElementById("heyfolks").innerText = "Salut les gens, je suis";
        document.getElementById("im1").innerText = "Développeur web";
        document.getElementById("im2").innerText = "Hacker éthique";
        document.getElementById("im3").innerText = "Spécialiste sécurité";
        // Update navigation menu
        const aboutLink = document.querySelector('a[href="#about"] span');
        const projectLink = document.querySelector('a[href="#project"] span');
        const resumeLink = document.querySelector('a[href="#resume"] span');
        const skillsLink = document.querySelector('a[href="#skills"] span');
        const contactLink = document.querySelector('a[href="#contact"] span');

        if (aboutLink) aboutLink.textContent = "À propos";
        if (projectLink) projectLink.textContent = "Projets";
        if (resumeLink) resumeLink.textContent = "CV";
        if (skillsLink) skillsLink.textContent = "Compétences";
        if (contactLink) contactLink.textContent = "Contact";
        language = "fr";
        updateProjectContent();
    }
})